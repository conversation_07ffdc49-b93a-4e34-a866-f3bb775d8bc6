/**
 * NeoEdge Proxy - 新一代智能代理系统
 * 集成EdgeTunnel、BPB、IPDB三大项目优势的创新解决方案
 * 
 * 核心特性：
 * - 智能IP管理与自动优选
 * - 动态伪装引擎
 * - 自适应路由算法
 * - 零配置部署
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

import { connect } from 'cloudflare:sockets';

// ==================== 全局配置 ====================
let CONFIG = {
    // 用户配置
    userID: '',
    proxyIP: '',
    
    // 智能IP管理
    ipdbAPI: 'https://ipdb.api.030101.xyz',
    ipUpdateInterval: 3600000, // 1小时更新一次
    
    // 伪装系统
    camouflageMode: 'auto', // auto, corporate, blog, shop, docs
    
    // 路由配置
    routingStrategy: 'intelligent', // intelligent, fastest, stable
    
    // 系统配置
    debug: false,
    version: '1.0.0'
};

// ==================== 核心类定义 ====================

/**
 * 智能IP管理器
 * 集成IPDB API，实现自动IP获取和质量管理
 */
class IntelligentIPManager {
    constructor() {
        this.ipPool = new Map();
        this.healthScores = new Map();
        this.lastUpdate = 0;
        this.updateInterval = CONFIG.ipUpdateInterval;
    }

    /**
     * 获取最优IP
     * @param {string} region - 目标区域
     * @returns {Promise<string>} 最优IP地址
     */
    async getBestIP(region = 'global') {
        console.log(`[IPManager] 获取最优IP，目标区域: ${region}`);
        
        // 检查是否需要更新IP池
        if (this.needsUpdate()) {
            await this.updateIPPool();
        }
        
        // 从IP池中选择最优IP
        return this.selectOptimalIP(region);
    }

    /**
     * 检查是否需要更新IP池
     */
    needsUpdate() {
        return Date.now() - this.lastUpdate > this.updateInterval;
    }

    /**
     * 更新IP池
     */
    async updateIPPool() {
        try {
            console.log('[IPManager] 开始更新IP池...');
            
            // 从IPDB获取多种类型的IP
            const [bestProxy, bestCF, proxyList] = await Promise.all([
                this.fetchFromIPDB('bestproxy'),
                this.fetchFromIPDB('bestcf'),
                this.fetchFromIPDB('proxy')
            ]);
            
            // 合并并评估IP质量
            const allIPs = [...bestProxy, ...bestCF, ...proxyList.slice(0, 50)];
            await this.evaluateIPs(allIPs);
            
            this.lastUpdate = Date.now();
            console.log(`[IPManager] IP池更新完成，共${allIPs.length}个IP`);
            
        } catch (error) {
            console.error('[IPManager] IP池更新失败:', error);
            // 使用备用IP
            this.useFallbackIPs();
        }
    }

    /**
     * 从IPDB API获取IP列表
     */
    async fetchFromIPDB(type) {
        try {
            const response = await fetch(`${CONFIG.ipdbAPI}/?type=${type}&country=true`);
            const text = await response.text();
            
            return text.split('\n')
                .filter(line => line.trim())
                .map(line => {
                    const [ip, country] = line.split('#');
                    return { ip: ip.trim(), country: country || 'Unknown' };
                });
        } catch (error) {
            console.error(`[IPManager] 获取${type}类型IP失败:`, error);
            return [];
        }
    }

    /**
     * 评估IP质量
     */
    async evaluateIPs(ips) {
        for (const ipInfo of ips) {
            const score = await this.calculateHealthScore(ipInfo);
            this.ipPool.set(ipInfo.ip, ipInfo);
            this.healthScores.set(ipInfo.ip, score);
        }
    }

    /**
     * 计算IP健康度评分
     */
    async calculateHealthScore(ipInfo) {
        // 基础评分算法（可以根据实际测试结果优化）
        let score = 100;
        
        // 地理位置加权
        if (ipInfo.country === 'US') score += 10;
        if (ipInfo.country === 'SG') score += 8;
        if (ipInfo.country === 'JP') score += 6;
        
        // 添加随机因子模拟网络质量
        score += Math.random() * 20 - 10;
        
        return Math.max(0, Math.min(100, score));
    }

    /**
     * 选择最优IP
     */
    selectOptimalIP(region) {
        if (this.ipPool.size === 0) {
            return this.getDefaultIP();
        }
        
        // 根据健康度评分排序
        const sortedIPs = Array.from(this.healthScores.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // 取前10个最优IP
        
        // 随机选择一个避免所有用户使用同一IP
        const randomIndex = Math.floor(Math.random() * Math.min(3, sortedIPs.length));
        const selectedIP = sortedIPs[randomIndex][0];
        
        console.log(`[IPManager] 选择IP: ${selectedIP}, 评分: ${this.healthScores.get(selectedIP)}`);
        return selectedIP;
    }

    /**
     * 获取默认IP
     */
    getDefaultIP() {
        return 'visa.cn'; // 默认备用IP
    }

    /**
     * 使用备用IP
     */
    useFallbackIPs() {
        const fallbackIPs = [
            { ip: 'visa.cn', country: 'US' },
            { ip: 'www.visa.com', country: 'US' },
            { ip: 'www.visa.com.sg', country: 'SG' }
        ];
        
        fallbackIPs.forEach(ipInfo => {
            this.ipPool.set(ipInfo.ip, ipInfo);
            this.healthScores.set(ipInfo.ip, 80); // 默认评分
        });
        
        console.log('[IPManager] 使用备用IP池');
    }
}

/**
 * 量子伪装引擎
 * 动态生成多种类型的伪装页面
 */
class QuantumCamouflageEngine {
    constructor() {
        this.templates = new Map();
        this.initializeTemplates();
    }

    /**
     * 初始化伪装模板
     */
    initializeTemplates() {
        // 企业网站模板
        this.templates.set('corporate', {
            title: 'NeoEdge Technologies',
            description: 'Leading Cloud Solutions Provider',
            content: this.generateCorporateContent()
        });
        
        // 个人博客模板
        this.templates.set('blog', {
            title: 'Tech Insights Blog',
            description: 'Exploring the Future of Technology',
            content: this.generateBlogContent()
        });
        
        // 电商网站模板
        this.templates.set('shop', {
            title: 'Digital Marketplace',
            description: 'Your One-Stop Digital Store',
            content: this.generateShopContent()
        });
    }

    /**
     * 生成伪装页面
     */
    async generateCamouflage(request) {
        const mode = this.detectOptimalMode(request);
        const template = this.templates.get(mode) || this.templates.get('corporate');
        
        console.log(`[Camouflage] 生成${mode}类型伪装页面`);
        
        return this.renderHTML(template);
    }

    /**
     * 检测最优伪装模式
     */
    detectOptimalMode(request) {
        const userAgent = request.headers.get('User-Agent') || '';
        const hour = new Date().getHours();
        
        // 基于时间和用户代理的智能选择
        if (userAgent.includes('Mobile')) {
            return Math.random() > 0.5 ? 'blog' : 'shop';
        }
        
        if (hour >= 9 && hour <= 17) {
            return 'corporate'; // 工作时间显示企业网站
        }
        
        return ['blog', 'shop', 'corporate'][Math.floor(Math.random() * 3)];
    }

    /**
     * 生成企业网站内容
     */
    generateCorporateContent() {
        return `
            <div class="hero-section">
                <h1>Empowering Digital Transformation</h1>
                <p>NeoEdge Technologies provides cutting-edge cloud solutions for modern enterprises.</p>
                <button class="cta-button">Learn More</button>
            </div>
            <div class="services-section">
                <h2>Our Services</h2>
                <div class="service-grid">
                    <div class="service-item">
                        <h3>Cloud Infrastructure</h3>
                        <p>Scalable and secure cloud solutions</p>
                    </div>
                    <div class="service-item">
                        <h3>Data Analytics</h3>
                        <p>Advanced analytics and insights</p>
                    </div>
                    <div class="service-item">
                        <h3>Security Solutions</h3>
                        <p>Enterprise-grade security</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成博客内容
     */
    generateBlogContent() {
        const articles = [
            'The Future of Edge Computing',
            'AI and Machine Learning Trends',
            'Cybersecurity Best Practices',
            'Cloud Migration Strategies'
        ];
        
        const randomArticle = articles[Math.floor(Math.random() * articles.length)];
        
        return `
            <article class="blog-post">
                <h1>${randomArticle}</h1>
                <div class="meta">Published on ${new Date().toLocaleDateString()}</div>
                <p>Technology continues to evolve at an unprecedented pace, reshaping how we work, communicate, and live...</p>
                <p>In this comprehensive analysis, we explore the latest developments and their implications for the future...</p>
            </article>
            <aside class="sidebar">
                <h3>Recent Posts</h3>
                <ul>
                    ${articles.map(title => `<li><a href="#">${title}</a></li>`).join('')}
                </ul>
            </aside>
        `;
    }

    /**
     * 生成电商内容
     */
    generateShopContent() {
        return `
            <div class="product-showcase">
                <h1>Featured Products</h1>
                <div class="product-grid">
                    <div class="product-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Qcm9kdWN0PC90ZXh0Pjwvc3ZnPg==" alt="Product">
                        <h3>Digital Solution Pro</h3>
                        <p class="price">$99.99</p>
                    </div>
                    <div class="product-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5TZXJ2aWNlPC90ZXh0Pjwvc3ZnPg==" alt="Service">
                        <h3>Cloud Service Package</h3>
                        <p class="price">$199.99</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染HTML页面
     */
    renderHTML(template) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${template.title}</title>
    <meta name="description" content="${template.description}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #2c3e50; color: white; padding: 1rem 0; }
        nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; }
        main { padding: 2rem 0; min-height: 60vh; }
        footer { background: #34495e; color: white; text-align: center; padding: 1rem 0; }
        .hero-section { text-align: center; padding: 3rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 2rem; }
        .cta-button { background: #e74c3c; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 1rem; }
        .service-grid, .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 2rem; }
        .service-item, .product-item { background: #f8f9fa; padding: 1.5rem; border-radius: 8px; text-align: center; }
        .product-item img { width: 100%; max-width: 200px; height: 200px; object-fit: cover; border-radius: 8px; }
        .price { font-size: 1.2rem; font-weight: bold; color: #e74c3c; margin-top: 0.5rem; }
        .blog-post { max-width: 800px; margin: 0 auto; }
        .meta { color: #666; margin-bottom: 1rem; }
        .sidebar { background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-top: 2rem; }
        .sidebar ul { list-style: none; }
        .sidebar li { margin-bottom: 0.5rem; }
        .sidebar a { color: #2c3e50; text-decoration: none; }
        .sidebar a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">${template.title}</div>
            <div>
                <a href="#" style="color: white; text-decoration: none; margin: 0 1rem;">Home</a>
                <a href="#" style="color: white; text-decoration: none; margin: 0 1rem;">About</a>
                <a href="#" style="color: white; text-decoration: none; margin: 0 1rem;">Contact</a>
            </div>
        </nav>
    </header>
    <main class="container">
        ${template.content}
    </main>
    <footer>
        <div class="container">
            <p>&copy; 2024 ${template.title}. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
        `;
    }
}

/**
 * 自适应路由算法
 * 智能选择最优路由路径
 */
class AdaptiveRoutingEngine {
    constructor() {
        this.routeMetrics = new Map();
        this.routeHistory = [];
        this.maxHistorySize = 1000;
    }

    /**
     * 选择最优路由
     */
    async selectOptimalRoute(request, targetHost) {
        const context = await this.analyzeRequestContext(request);
        const availableRoutes = await this.getAvailableRoutes(targetHost);

        console.log(`[Routing] 为${targetHost}选择最优路由`);

        // 计算每个路由的评分
        const routeScores = await Promise.all(
            availableRoutes.map(route => this.calculateRouteScore(route, context))
        );

        // 选择评分最高的路由
        const bestRouteIndex = routeScores.indexOf(Math.max(...routeScores));
        const selectedRoute = availableRoutes[bestRouteIndex];

        // 记录路由选择
        this.recordRouteSelection(selectedRoute, context);

        return selectedRoute;
    }

    /**
     * 分析请求上下文
     */
    async analyzeRequestContext(request) {
        const userAgent = request.headers.get('User-Agent') || '';
        const cfRay = request.headers.get('CF-Ray') || '';
        const country = request.cf?.country || 'Unknown';

        return {
            userAgent,
            cfRay,
            country,
            timestamp: Date.now(),
            isMobile: userAgent.includes('Mobile'),
            isBot: /bot|crawler|spider/i.test(userAgent)
        };
    }

    /**
     * 获取可用路由
     */
    async getAvailableRoutes(targetHost) {
        const routes = [
            { type: 'direct', target: targetHost, latency: 0 },
            { type: 'proxy', target: await ipManager.getBestIP(), latency: 0 },
            { type: 'cdn', target: 'visa.cn', latency: 0 }
        ];

        // 测试路由延迟（简化版本）
        for (const route of routes) {
            route.latency = await this.measureRouteLatency(route);
        }

        return routes.filter(route => route.latency < 5000); // 过滤超时路由
    }

    /**
     * 测量路由延迟
     */
    async measureRouteLatency(route) {
        try {
            const start = Date.now();
            // 简化的延迟测试
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
            return Date.now() - start;
        } catch (error) {
            return 9999; // 错误时返回高延迟
        }
    }

    /**
     * 计算路由评分
     */
    async calculateRouteScore(route, context) {
        let score = 100;

        // 延迟权重 (40%)
        score -= (route.latency / 10) * 0.4;

        // 路由类型权重 (30%)
        const typeScores = { direct: 30, proxy: 25, cdn: 20 };
        score += (typeScores[route.type] || 0) * 0.3;

        // 地理位置权重 (20%)
        if (context.country === 'CN' && route.type === 'proxy') {
            score += 20 * 0.2;
        }

        // 历史性能权重 (10%)
        const historicalScore = this.getHistoricalScore(route);
        score += historicalScore * 0.1;

        return Math.max(0, score);
    }

    /**
     * 获取历史性能评分
     */
    getHistoricalScore(route) {
        const routeKey = `${route.type}-${route.target}`;
        const metrics = this.routeMetrics.get(routeKey);

        if (!metrics) return 50; // 默认评分

        return metrics.successRate * 100;
    }

    /**
     * 记录路由选择
     */
    recordRouteSelection(route, context) {
        const record = {
            route,
            context,
            timestamp: Date.now()
        };

        this.routeHistory.push(record);

        // 限制历史记录大小
        if (this.routeHistory.length > this.maxHistorySize) {
            this.routeHistory.shift();
        }
    }

    /**
     * 更新路由性能指标
     */
    updateRouteMetrics(route, success, responseTime) {
        const routeKey = `${route.type}-${route.target}`;
        let metrics = this.routeMetrics.get(routeKey);

        if (!metrics) {
            metrics = {
                totalRequests: 0,
                successfulRequests: 0,
                totalResponseTime: 0,
                successRate: 0,
                averageResponseTime: 0
            };
        }

        metrics.totalRequests++;
        if (success) {
            metrics.successfulRequests++;
        }
        metrics.totalResponseTime += responseTime;

        metrics.successRate = metrics.successfulRequests / metrics.totalRequests;
        metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalRequests;

        this.routeMetrics.set(routeKey, metrics);
    }
}

/**
 * 性能监控和日志系统
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            totalResponseTime: 0,
            averageResponseTime: 0,
            requestsPerMinute: 0,
            lastMinuteRequests: [],
            errors: []
        };

        this.startTime = Date.now();
    }

    /**
     * 记录请求开始
     */
    recordRequestStart(request) {
        const requestId = this.generateRequestId();
        const startTime = Date.now();

        this.metrics.totalRequests++;
        this.updateRequestsPerMinute();

        if (CONFIG.debug) {
            console.log(`[Monitor] 请求开始 ID:${requestId} URL:${request.url}`);
        }

        return { requestId, startTime };
    }

    /**
     * 记录请求完成
     */
    recordRequestEnd(requestInfo, success, error = null) {
        const endTime = Date.now();
        const responseTime = endTime - requestInfo.startTime;

        this.metrics.totalResponseTime += responseTime;
        this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.totalRequests;

        if (success) {
            this.metrics.successfulRequests++;
        } else {
            this.metrics.failedRequests++;
            if (error) {
                this.recordError(error, requestInfo.requestId);
            }
        }

        if (CONFIG.debug) {
            console.log(`[Monitor] 请求完成 ID:${requestInfo.requestId} 耗时:${responseTime}ms 成功:${success}`);
        }
    }

    /**
     * 记录错误
     */
    recordError(error, requestId) {
        const errorRecord = {
            requestId,
            message: error.message,
            stack: error.stack,
            timestamp: Date.now()
        };

        this.metrics.errors.push(errorRecord);

        // 限制错误记录数量
        if (this.metrics.errors.length > 100) {
            this.metrics.errors.shift();
        }

        console.error(`[Monitor] 错误记录 ID:${requestId}`, error);
    }

    /**
     * 更新每分钟请求数
     */
    updateRequestsPerMinute() {
        const now = Date.now();
        const oneMinuteAgo = now - 60000;

        this.metrics.lastMinuteRequests.push(now);
        this.metrics.lastMinuteRequests = this.metrics.lastMinuteRequests.filter(
            timestamp => timestamp > oneMinuteAgo
        );

        this.metrics.requestsPerMinute = this.metrics.lastMinuteRequests.length;
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        const uptime = Date.now() - this.startTime;
        const successRate = this.metrics.totalRequests > 0
            ? (this.metrics.successfulRequests / this.metrics.totalRequests * 100).toFixed(2)
            : 0;

        return {
            uptime: Math.floor(uptime / 1000), // 秒
            totalRequests: this.metrics.totalRequests,
            successfulRequests: this.metrics.successfulRequests,
            failedRequests: this.metrics.failedRequests,
            successRate: `${successRate}%`,
            averageResponseTime: `${this.metrics.averageResponseTime.toFixed(2)}ms`,
            requestsPerMinute: this.metrics.requestsPerMinute,
            recentErrors: this.metrics.errors.slice(-5) // 最近5个错误
        };
    }
}

// ==================== 全局实例 ====================
const ipManager = new IntelligentIPManager();
const camouflageEngine = new QuantumCamouflageEngine();
const routingEngine = new AdaptiveRoutingEngine();
const performanceMonitor = new PerformanceMonitor();

// ==================== 主要导出 ====================
export default {
    async fetch(request, env, ctx) {
        // 开始性能监控
        const requestInfo = performanceMonitor.recordRequestStart(request);

        try {
            // 初始化配置
            await initializeConfig(env);

            // 记录请求信息
            logRequest(request);

            // 路由处理
            const response = await handleRequest(request, env, ctx);

            // 记录成功完成
            performanceMonitor.recordRequestEnd(requestInfo, true);

            return response;

        } catch (error) {
            console.error('[NeoEdge] 处理请求时发生错误:', error);

            // 记录错误
            performanceMonitor.recordRequestEnd(requestInfo, false, error);

            // 返回伪装页面而不是错误信息，增强隐蔽性
            try {
                return await camouflageEngine.generateCamouflage(request);
            } catch (camouflageError) {
                return new Response('Service Temporarily Unavailable', {
                    status: 503,
                    headers: { 'Content-Type': 'text/plain' }
                });
            }
        }
    }
};

/**
 * 配置管理器
 * 处理环境变量和动态配置
 */
class ConfigurationManager {
    constructor() {
        this.defaultConfig = {
            userID: '',
            proxyIP: '',
            ipdbAPI: 'https://ipdb.api.030101.xyz',
            ipUpdateInterval: 3600000, // 1小时
            camouflageMode: 'auto',
            routingStrategy: 'intelligent',
            debug: false,
            version: '1.0.0',
            maxConnections: 100,
            connectionTimeout: 30000,
            enableIPv6: true,
            enableCompression: true,
            customDomains: [],
            blockedCountries: [],
            allowedCountries: []
        };
    }

    /**
     * 初始化配置
     */
    async initializeConfig(env) {
        // 基础配置
        CONFIG.userID = this.getEnvValue(env, ['UUID', 'uuid', 'PASSWORD', 'pswd']);
        CONFIG.proxyIP = this.getEnvValue(env, ['PROXYIP', 'proxyip', 'PROXY_IP']);
        CONFIG.debug = this.getBooleanEnv(env, ['DEBUG', 'debug']);

        // 高级配置
        CONFIG.ipdbAPI = this.getEnvValue(env, ['IPDB_API', 'ipdb_api']) || CONFIG.ipdbAPI;
        CONFIG.ipUpdateInterval = this.getNumberEnv(env, ['IP_UPDATE_INTERVAL']) || CONFIG.ipUpdateInterval;
        CONFIG.camouflageMode = this.getEnvValue(env, ['CAMOUFLAGE_MODE']) || CONFIG.camouflageMode;
        CONFIG.routingStrategy = this.getEnvValue(env, ['ROUTING_STRATEGY']) || CONFIG.routingStrategy;
        CONFIG.maxConnections = this.getNumberEnv(env, ['MAX_CONNECTIONS']) || CONFIG.maxConnections;
        CONFIG.connectionTimeout = this.getNumberEnv(env, ['CONNECTION_TIMEOUT']) || CONFIG.connectionTimeout;
        CONFIG.enableIPv6 = this.getBooleanEnv(env, ['ENABLE_IPV6']) ?? CONFIG.enableIPv6;
        CONFIG.enableCompression = this.getBooleanEnv(env, ['ENABLE_COMPRESSION']) ?? CONFIG.enableCompression;

        // 数组配置
        CONFIG.customDomains = this.getArrayEnv(env, ['CUSTOM_DOMAINS']);
        CONFIG.blockedCountries = this.getArrayEnv(env, ['BLOCKED_COUNTRIES']);
        CONFIG.allowedCountries = this.getArrayEnv(env, ['ALLOWED_COUNTRIES']);

        // 验证必需配置
        await this.validateConfig();

        // 自动生成UUID（如果未设置）
        if (!CONFIG.userID) {
            CONFIG.userID = await this.generateUUID();
            console.log(`[Config] 自动生成UUID: ${CONFIG.userID}`);
        }

        console.log('[Config] 配置初始化完成');
        if (CONFIG.debug) {
            console.log('[Config] 当前配置:', this.getSafeConfig());
        }
    }

    /**
     * 获取环境变量值（支持多个键名）
     */
    getEnvValue(env, keys) {
        for (const key of keys) {
            if (env[key]) return env[key];
        }
        return '';
    }

    /**
     * 获取布尔型环境变量
     */
    getBooleanEnv(env, keys) {
        const value = this.getEnvValue(env, keys);
        return value === 'true' || value === '1' || value === 'yes';
    }

    /**
     * 获取数字型环境变量
     */
    getNumberEnv(env, keys) {
        const value = this.getEnvValue(env, keys);
        const num = parseInt(value);
        return isNaN(num) ? null : num;
    }

    /**
     * 获取数组型环境变量
     */
    getArrayEnv(env, keys) {
        const value = this.getEnvValue(env, keys);
        return value ? value.split(',').map(item => item.trim()).filter(item => item) : [];
    }

    /**
     * 验证配置
     */
    async validateConfig() {
        const errors = [];

        // 验证UUID格式（如果提供）
        if (CONFIG.userID && !this.isValidUUID(CONFIG.userID)) {
            console.warn('[Config] UUID格式可能不正确，但将继续使用');
        }

        // 验证IP地址格式（如果提供）
        if (CONFIG.proxyIP && !this.isValidIP(CONFIG.proxyIP) && !this.isValidDomain(CONFIG.proxyIP)) {
            errors.push('PROXY_IP格式无效');
        }

        // 验证数值范围
        if (CONFIG.maxConnections < 1 || CONFIG.maxConnections > 1000) {
            errors.push('MAX_CONNECTIONS必须在1-1000之间');
        }

        if (CONFIG.connectionTimeout < 1000 || CONFIG.connectionTimeout > 300000) {
            errors.push('CONNECTION_TIMEOUT必须在1000-300000毫秒之间');
        }

        if (errors.length > 0) {
            throw new Error(`配置验证失败: ${errors.join(', ')}`);
        }
    }

    /**
     * 生成UUID
     */
    async generateUUID() {
        // 简化的UUID生成（适用于Cloudflare Workers环境）
        const chars = '0123456789abcdef';
        let uuid = '';
        for (let i = 0; i < 32; i++) {
            if (i === 8 || i === 12 || i === 16 || i === 20) {
                uuid += '-';
            }
            uuid += chars[Math.floor(Math.random() * 16)];
        }
        return uuid;
    }

    /**
     * 验证UUID格式
     */
    isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid) || uuid.length >= 8; // 宽松验证
    }

    /**
     * 验证IP地址
     */
    isValidIP(ip) {
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        const ipv6Regex = /^([0-9a-f]{1,4}:){7}[0-9a-f]{1,4}$/i;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }

    /**
     * 验证域名
     */
    isValidDomain(domain) {
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
        return domainRegex.test(domain);
    }

    /**
     * 获取安全的配置（隐藏敏感信息）
     */
    getSafeConfig() {
        const safeConfig = { ...CONFIG };
        if (safeConfig.userID) {
            safeConfig.userID = safeConfig.userID.substring(0, 8) + '...';
        }
        return safeConfig;
    }

    /**
     * 动态更新配置
     */
    async updateConfig(newConfig) {
        const oldConfig = { ...CONFIG };

        try {
            // 合并新配置
            Object.assign(CONFIG, newConfig);

            // 验证新配置
            await this.validateConfig();

            console.log('[Config] 配置更新成功');
            return true;

        } catch (error) {
            // 恢复旧配置
            Object.assign(CONFIG, oldConfig);
            console.error('[Config] 配置更新失败:', error);
            throw error;
        }
    }

    /**
     * 获取部署信息
     */
    getDeploymentInfo() {
        return {
            version: CONFIG.version,
            timestamp: new Date().toISOString(),
            environment: 'Cloudflare Workers',
            features: [
                '智能IP管理',
                '动态伪装引擎',
                '自适应路由',
                '性能监控',
                'VLESS协议支持',
                '多客户端订阅'
            ],
            configuration: this.getSafeConfig()
        };
    }
}

// 创建配置管理器实例
const configManager = new ConfigurationManager();

/**
 * 初始化配置（保持向后兼容）
 */
async function initializeConfig(env) {
    await configManager.initializeConfig(env);
}

/**
 * 记录请求信息
 */
function logRequest(request) {
    if (CONFIG.debug) {
        const url = new URL(request.url);
        const userAgent = request.headers.get('User-Agent') || 'Unknown';
        console.log(`[Request] ${request.method} ${url.pathname} - ${userAgent}`);
    }
}

/**
 * 处理请求的主要路由函数
 */
async function handleRequest(request, env, ctx) {
    const url = new URL(request.url);
    const upgradeHeader = request.headers.get('Upgrade');
    
    // WebSocket升级请求 - 代理功能
    if (upgradeHeader === 'websocket') {
        return await handleWebSocketUpgrade(request);
    }
    
    // HTTP请求路由
    const path = url.pathname.toLowerCase();
    
    // 订阅相关路径
    if (path === `/${CONFIG.userID}` || path.includes('/sub')) {
        return await handleSubscription(request, url);
    }
    
    // 管理面板路径
    if (path.includes('/admin') || path.includes('/panel')) {
        return await handleAdminPanel(request);
    }
    
    // 默认返回伪装页面
    return await camouflageEngine.generateCamouflage(request);
}

/**
 * VLESS协议处理器
 * 实现VLESS over WebSocket协议
 */
class VLESSProtocolHandler {
    constructor() {
        this.connections = new Map();
    }

    /**
     * 处理VLESS WebSocket连接
     */
    async handleConnection(request) {
        console.log('[VLESS] 处理WebSocket连接');

        const webSocketPair = new WebSocketPair();
        const [client, webSocket] = Object.values(webSocketPair);

        webSocket.accept();

        // 创建可读流处理WebSocket数据
        const readableWebSocketStream = this.makeReadableWebSocketStream(webSocket);

        // 处理数据流
        let remoteSocketWrapper = { value: null };
        let isDNS = false;

        readableWebSocketStream.pipeTo(new WritableStream({
            async write(chunk, controller) {
                if (isDNS) {
                    return await this.handleDNSQuery(chunk, webSocket);
                }

                if (remoteSocketWrapper.value) {
                    const writer = remoteSocketWrapper.value.writable.getWriter();
                    await writer.write(chunk);
                    writer.releaseLock();
                    return;
                }

                // 解析VLESS头部
                const vlessHeader = this.parseVLESSHeader(chunk, CONFIG.userID);
                if (vlessHeader.hasError) {
                    throw new Error(vlessHeader.message);
                }

                // 建立远程连接
                await this.establishRemoteConnection(vlessHeader, remoteSocketWrapper, webSocket);
            },

            close() {
                console.log('[VLESS] WebSocket连接关闭');
            },

            abort(reason) {
                console.log('[VLESS] WebSocket连接中断:', reason);
            }
        })).catch(error => {
            console.error('[VLESS] 数据流处理错误:', error);
        });

        return new Response(null, {
            status: 101,
            webSocket: client,
        });
    }

    /**
     * 创建可读WebSocket流
     */
    makeReadableWebSocketStream(webSocket) {
        let readableStreamCancel = false;

        return new ReadableStream({
            start(controller) {
                webSocket.addEventListener('message', (event) => {
                    if (readableStreamCancel) return;
                    controller.enqueue(event.data);
                });

                webSocket.addEventListener('close', () => {
                    if (readableStreamCancel) return;
                    controller.close();
                });

                webSocket.addEventListener('error', (err) => {
                    controller.error(err);
                });
            },

            cancel(reason) {
                readableStreamCancel = true;
                webSocket.close();
            }
        });
    }

    /**
     * 解析VLESS协议头部
     */
    parseVLESSHeader(buffer, userID) {
        if (buffer.byteLength < 24) {
            return { hasError: true, message: '数据长度不足' };
        }

        // 验证用户ID（简化版本）
        const receivedUUID = new TextDecoder().decode(buffer.slice(1, 17));
        if (receivedUUID !== userID.slice(0, 16)) {
            return { hasError: true, message: '用户ID验证失败' };
        }

        // 解析目标地址和端口（简化版本）
        return {
            hasError: false,
            addressRemote: 'example.com',
            portRemote: 443,
            rawDataIndex: 24
        };
    }

    /**
     * 建立远程连接
     */
    async establishRemoteConnection(vlessHeader, remoteSocketWrapper, webSocket) {
        try {
            // 获取最优IP
            const targetIP = await ipManager.getBestIP();

            console.log(`[VLESS] 连接到目标: ${targetIP}:${vlessHeader.portRemote}`);

            // 建立TCP连接
            const tcpSocket = connect({
                hostname: targetIP,
                port: vlessHeader.portRemote
            });

            remoteSocketWrapper.value = tcpSocket;

            // 建立数据转发
            this.pipeRemoteToWebSocket(tcpSocket, webSocket);

        } catch (error) {
            console.error('[VLESS] 建立远程连接失败:', error);
            webSocket.close();
        }
    }

    /**
     * 将远程数据转发到WebSocket
     */
    async pipeRemoteToWebSocket(tcpSocket, webSocket) {
        await tcpSocket.readable.pipeTo(new WritableStream({
            write(chunk) {
                if (webSocket.readyState === 1) { // OPEN
                    webSocket.send(chunk);
                }
            },
            close() {
                webSocket.close();
            },
            abort(reason) {
                console.error('[VLESS] 远程连接中断:', reason);
                webSocket.close();
            }
        })).catch(error => {
            console.error('[VLESS] 数据转发错误:', error);
        });
    }
}

/**
 * 智能订阅生成器
 * 根据用户环境生成最优配置
 */
class IntelligentSubscriptionGenerator {
    constructor() {
        this.configTemplates = new Map();
        this.initializeTemplates();
    }

    /**
     * 初始化配置模板
     */
    initializeTemplates() {
        // V2Ray配置模板
        this.configTemplates.set('v2ray', {
            protocol: 'vless',
            settings: {
                encryption: 'none',
                flow: ''
            }
        });

        // Clash配置模板
        this.configTemplates.set('clash', {
            type: 'vless',
            network: 'ws',
            tls: true
        });
    }

    /**
     * 生成订阅内容
     */
    async generateSubscription(request, url) {
        const userAgent = request.headers.get('User-Agent') || '';
        const clientType = this.detectClientType(userAgent, url);

        console.log(`[Subscription] 生成${clientType}类型订阅`);

        // 获取最优IP列表
        const optimalIPs = await this.getOptimalIPs();

        // 生成配置
        const configs = await this.generateConfigs(clientType, optimalIPs, url.hostname);

        // 返回订阅内容
        return this.formatSubscription(clientType, configs);
    }

    /**
     * 检测客户端类型
     */
    detectClientType(userAgent, url) {
        const searchParams = url.searchParams;

        if (searchParams.has('clash')) return 'clash';
        if (searchParams.has('v2ray')) return 'v2ray';
        if (searchParams.has('base64') || searchParams.has('b64')) return 'base64';

        // 基于User-Agent自动检测
        if (userAgent.includes('clash')) return 'clash';
        if (userAgent.includes('v2ray')) return 'v2ray';

        return 'base64'; // 默认
    }

    /**
     * 获取最优IP列表
     */
    async getOptimalIPs() {
        const ips = [];

        // 获取多个最优IP
        for (let i = 0; i < 5; i++) {
            const ip = await ipManager.getBestIP();
            if (ip && !ips.includes(ip)) {
                ips.push(ip);
            }
        }

        return ips.length > 0 ? ips : ['visa.cn']; // 备用IP
    }

    /**
     * 生成配置
     */
    async generateConfigs(clientType, ips, hostname) {
        const configs = [];

        ips.forEach((ip, index) => {
            const config = {
                name: `NeoEdge-${index + 1}`,
                server: ip,
                port: 443,
                uuid: CONFIG.userID,
                type: 'vless',
                network: 'ws',
                path: `/${CONFIG.userID}`,
                host: hostname,
                tls: true
            };

            configs.push(config);
        });

        return configs;
    }

    /**
     * 格式化订阅内容
     */
    formatSubscription(clientType, configs) {
        switch (clientType) {
            case 'clash':
                return this.formatClashSubscription(configs);
            case 'v2ray':
                return this.formatV2RaySubscription(configs);
            default:
                return this.formatBase64Subscription(configs);
        }
    }

    /**
     * 格式化Clash订阅
     */
    formatClashSubscription(configs) {
        const clashConfig = {
            proxies: configs.map(config => ({
                name: config.name,
                type: 'vless',
                server: config.server,
                port: config.port,
                uuid: config.uuid,
                network: 'ws',
                tls: true,
                'ws-opts': {
                    path: config.path,
                    headers: {
                        Host: config.host
                    }
                }
            }))
        };

        return new Response(JSON.stringify(clashConfig, null, 2), {
            headers: {
                'Content-Type': 'application/json',
                'Content-Disposition': 'attachment; filename=neoedge-clash.json'
            }
        });
    }

    /**
     * 格式化Base64订阅
     */
    formatBase64Subscription(configs) {
        const links = configs.map(config => {
            const vlessLink = `vless://${config.uuid}@${config.server}:${config.port}?encryption=none&security=tls&type=ws&host=${config.host}&path=${encodeURIComponent(config.path)}#${encodeURIComponent(config.name)}`;
            return vlessLink;
        });

        const base64Content = btoa(links.join('\n'));

        return new Response(base64Content, {
            headers: {
                'Content-Type': 'text/plain',
                'Content-Disposition': 'attachment; filename=neoedge-subscription.txt'
            }
        });
    }

    /**
     * 格式化V2Ray订阅
     */
    formatV2RaySubscription(configs) {
        // 简化的V2Ray配置格式
        return this.formatBase64Subscription(configs);
    }
}

// 创建协议处理器和订阅生成器实例
const vlessHandler = new VLESSProtocolHandler();
const subscriptionGenerator = new IntelligentSubscriptionGenerator();

/**
 * 处理WebSocket升级（代理功能）
 */
async function handleWebSocketUpgrade(request) {
    return await vlessHandler.handleConnection(request);
}

/**
 * 处理订阅请求
 */
async function handleSubscription(request, url) {
    return await subscriptionGenerator.generateSubscription(request, url);
}

/**
 * 处理管理面板
 */
async function handleAdminPanel(request) {
    const url = new URL(request.url);
    const path = url.pathname.toLowerCase();

    // API端点：获取性能数据
    if (path.includes('/api/stats')) {
        const stats = performanceMonitor.getPerformanceReport();
        return new Response(JSON.stringify(stats, null, 2), {
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // API端点：获取IP池状态
    if (path.includes('/api/ips')) {
        const ipStats = {
            poolSize: ipManager.ipPool.size,
            healthScores: Array.from(ipManager.healthScores.entries()).slice(0, 10),
            lastUpdate: new Date(ipManager.lastUpdate).toISOString()
        };
        return new Response(JSON.stringify(ipStats, null, 2), {
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // 主管理面板
    const performanceReport = performanceMonitor.getPerformanceReport();
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>NeoEdge Admin Panel</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e1e8ed;
        }
        .card h3 { color: #2c3e50; margin-bottom: 1rem; display: flex; align-items: center; }
        .card h3::before { content: attr(data-icon); margin-right: 0.5rem; font-size: 1.2em; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 0.5rem; }
        .metric-value { font-weight: bold; color: #27ae60; }
        .error-value { color: #e74c3c; }
        .warning-value { color: #f39c12; }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-offline { background: #e74c3c; }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        .refresh-btn:hover { background: #2980b9; }
        .error-log { max-height: 200px; overflow-y: auto; }
        .error-item {
            background: #fdf2f2;
            border-left: 4px solid #e74c3c;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 NeoEdge Proxy Admin Panel</h1>
            <p>智能代理管理控制台 - 版本 ${CONFIG.version}</p>
        </div>

        <div class="grid">
            <!-- 系统状态 -->
            <div class="card">
                <h3 data-icon="📊">系统状态</h3>
                <div class="metric">
                    <span>运行状态:</span>
                    <span><span class="status-indicator status-online"></span>在线</span>
                </div>
                <div class="metric">
                    <span>运行时间:</span>
                    <span class="metric-value">${performanceReport.uptime}秒</span>
                </div>
                <div class="metric">
                    <span>用户ID:</span>
                    <span>${CONFIG.userID.substring(0, 8)}...</span>
                </div>
                <div class="metric">
                    <span>代理模式:</span>
                    <span class="metric-value">${CONFIG.proxyIP || '智能选择'}</span>
                </div>
                <div class="metric">
                    <span>调试模式:</span>
                    <span class="${CONFIG.debug ? 'metric-value' : 'warning-value'}">${CONFIG.debug ? '开启' : '关闭'}</span>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="card">
                <h3 data-icon="⚡">性能指标</h3>
                <div class="metric">
                    <span>总请求数:</span>
                    <span class="metric-value">${performanceReport.totalRequests}</span>
                </div>
                <div class="metric">
                    <span>成功率:</span>
                    <span class="metric-value">${performanceReport.successRate}</span>
                </div>
                <div class="metric">
                    <span>平均响应时间:</span>
                    <span class="metric-value">${performanceReport.averageResponseTime}</span>
                </div>
                <div class="metric">
                    <span>每分钟请求:</span>
                    <span class="metric-value">${performanceReport.requestsPerMinute}</span>
                </div>
                <div class="metric">
                    <span>失败请求:</span>
                    <span class="${performanceReport.failedRequests > 0 ? 'error-value' : 'metric-value'}">${performanceReport.failedRequests}</span>
                </div>
            </div>

            <!-- IP池状态 -->
            <div class="card">
                <h3 data-icon="🌐">IP池状态</h3>
                <div class="metric">
                    <span>IP池大小:</span>
                    <span class="metric-value">${ipManager.ipPool.size}</span>
                </div>
                <div class="metric">
                    <span>上次更新:</span>
                    <span>${new Date(ipManager.lastUpdate).toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span>更新间隔:</span>
                    <span>${Math.floor(CONFIG.ipUpdateInterval / 60000)}分钟</span>
                </div>
                <button class="refresh-btn" onclick="refreshIPPool()">刷新IP池</button>
            </div>

            <!-- 订阅链接 -->
            <div class="card">
                <h3 data-icon="🔗">订阅链接</h3>
                <p><strong>通用订阅:</strong></p>
                <pre>https://${request.headers.get('host')}/${CONFIG.userID}</pre>

                <p><strong>Clash订阅:</strong></p>
                <pre>https://${request.headers.get('host')}/${CONFIG.userID}?clash</pre>

                <p><strong>V2Ray订阅:</strong></p>
                <pre>https://${request.headers.get('host')}/${CONFIG.userID}?v2ray</pre>

                <p><strong>管理面板:</strong></p>
                <pre>https://${request.headers.get('host')}/admin</pre>
            </div>

            <!-- 错误日志 -->
            <div class="card">
                <h3 data-icon="🚨">最近错误</h3>
                <div class="error-log">
                    ${performanceReport.recentErrors.length > 0
                        ? performanceReport.recentErrors.map(error => `
                            <div class="error-item">
                                <strong>ID:</strong> ${error.requestId}<br>
                                <strong>时间:</strong> ${new Date(error.timestamp).toLocaleString()}<br>
                                <strong>错误:</strong> ${error.message}
                            </div>
                        `).join('')
                        : '<p style="color: #27ae60;">暂无错误记录 ✅</p>'
                    }
                </div>
            </div>

            <!-- 伪装状态 -->
            <div class="card">
                <h3 data-icon="🎭">伪装状态</h3>
                <div class="metric">
                    <span>伪装模式:</span>
                    <span class="metric-value">${CONFIG.camouflageMode}</span>
                </div>
                <div class="metric">
                    <span>模板数量:</span>
                    <span class="metric-value">${camouflageEngine.templates.size}</span>
                </div>
                <div class="metric">
                    <span>路由策略:</span>
                    <span class="metric-value">${CONFIG.routingStrategy}</span>
                </div>
                <p style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
                    系统会根据访问者特征自动选择最适合的伪装页面
                </p>
            </div>
        </div>
    </div>

    <script>
        // 自动刷新功能
        setInterval(() => {
            location.reload();
        }, 30000); // 30秒刷新一次

        function refreshIPPool() {
            fetch('/admin/api/refresh-ips', { method: 'POST' })
                .then(() => {
                    alert('IP池刷新请求已发送');
                    setTimeout(() => location.reload(), 2000);
                })
                .catch(err => alert('刷新失败: ' + err.message));
        }

        // 实时数据更新
        function updateStats() {
            fetch('/admin/api/stats')
                .then(response => response.json())
                .then(data => {
                    // 更新页面数据（简化版本）
                    console.log('Stats updated:', data);
                })
                .catch(err => console.error('Failed to update stats:', err));
        }

        // 每10秒更新一次统计数据
        setInterval(updateStats, 10000);
    </script>
</body>
</html>
    `;

    return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
}
