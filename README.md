# 🚀 NeoEdge Proxy

**新一代智能代理系统** - 集成EdgeTunnel、BPB、IPDB三大项目优势的创新解决方案

## ✨ 核心特性

### 🧠 智能IP管理
- **自动IP优选**: 集成IPDB API，实时获取最优Cloudflare IP
- **健康度监控**: 智能评估IP质量，自动故障转移
- **地理位置优化**: 根据用户位置选择最佳节点

### 🎭 动态伪装引擎
- **量子伪装**: 动态生成企业网站、个人博客、电商等多种伪装页面
- **智能切换**: 根据访问者特征自动选择最适合的伪装类型
- **反检测**: 高级流量混淆和特征隐藏技术

### 🔄 自适应路由
- **智能决策**: AI驱动的路由选择算法
- **性能优化**: 实时网络质量检测和路径优化
- **负载均衡**: 动态分配流量，避免单点过载

### 📊 性能监控
- **实时统计**: 请求数、成功率、响应时间等关键指标
- **错误追踪**: 详细的错误日志和故障分析
- **可视化面板**: 直观的Web管理界面

## 🚀 快速部署

### 1. 准备工作
- Cloudflare账户
- 一个域名（可选，可使用workers.dev子域名）

### 2. 部署到Cloudflare Workers

#### 方法一：直接部署
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 `Workers & Pages` > `Create application` > `Create Worker`
3. 将 `_worker.js` 的内容完整复制到编辑器中
4. 点击 `Save and Deploy`

#### 方法二：使用Wrangler CLI
```bash
# 安装Wrangler
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 创建项目
wrangler init neoedge-proxy

# 复制_worker.js内容到src/index.js
# 部署
wrangler deploy
```

### 3. 配置环境变量

在Cloudflare Workers设置中添加以下环境变量：

#### 🔴 必需变量
| 变量名 | 说明 | 示例 |
|--------|------|------|
| `UUID` | 用户唯一标识符 | `550e8400-e29b-41d4-a716-************` |

#### 🟡 可选变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `PROXYIP` | 指定代理IP | 自动选择 | `visa.cn` |
| `DEBUG` | 调试模式 | `false` | `true` |
| `CAMOUFLAGE_MODE` | 伪装模式 | `auto` | `corporate` |
| `ROUTING_STRATEGY` | 路由策略 | `intelligent` | `fastest` |
| `MAX_CONNECTIONS` | 最大连接数 | `100` | `200` |
| `ENABLE_IPV6` | 启用IPv6 | `true` | `false` |

#### 🔵 高级变量
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `IPDB_API` | IPDB API地址 | `https://ipdb.api.030101.xyz` |
| `IP_UPDATE_INTERVAL` | IP更新间隔(毫秒) | `3600000` |
| `CONNECTION_TIMEOUT` | 连接超时(毫秒) | `30000` |
| `CUSTOM_DOMAINS` | 自定义域名列表 | 空 |
| `BLOCKED_COUNTRIES` | 屏蔽国家列表 | 空 |
| `ALLOWED_COUNTRIES` | 允许国家列表 | 空 |

### 4. 绑定自定义域名（可选）

1. 在Cloudflare Workers设置中点击 `Triggers`
2. 点击 `Add Custom Domain`
3. 输入你的域名（需要在Cloudflare托管）
4. 等待SSL证书生成完成

## 📱 客户端配置

### 获取订阅链接

部署完成后，访问以下链接获取订阅：

- **通用订阅**: `https://your-domain.com/your-uuid`
- **Clash订阅**: `https://your-domain.com/your-uuid?clash`
- **V2Ray订阅**: `https://your-domain.com/your-uuid?v2ray`

### 支持的客户端

| 客户端 | 平台 | 订阅格式 |
|--------|------|----------|
| V2Ray | Windows/Mac/Linux | Base64 |
| V2RayN | Windows | Base64 |
| V2RayNG | Android | Base64 |
| Clash | 全平台 | Clash |
| ClashX | macOS | Clash |
| Clash for Android | Android | Clash |
| Shadowrocket | iOS | Base64 |
| Quantumult X | iOS | Base64 |

## 🛠️ 管理面板

访问 `https://your-domain.com/admin` 进入管理面板，可以查看：

- 📊 系统运行状态
- ⚡ 性能指标统计
- 🌐 IP池健康状况
- 🔗 订阅链接管理
- 🚨 错误日志查看
- 🎭 伪装状态监控

## 🔧 高级配置

### 伪装模式说明

| 模式 | 说明 | 适用场景 |
|------|------|----------|
| `auto` | 自动选择 | 推荐，根据访问特征智能选择 |
| `corporate` | 企业网站 | 工作时间访问 |
| `blog` | 个人博客 | 移动设备访问 |
| `shop` | 电商网站 | 随机访问 |

### 路由策略说明

| 策略 | 说明 | 特点 |
|------|------|------|
| `intelligent` | 智能路由 | 综合考虑延迟、成功率、地理位置 |
| `fastest` | 最快路由 | 优先选择延迟最低的路径 |
| `stable` | 稳定路由 | 优先选择成功率最高的路径 |

## 🔍 故障排除

### 常见问题

#### 1. UUID未设置错误
**错误**: `UUID未设置，请在环境变量中配置UUID`
**解决**: 在Workers环境变量中添加 `UUID` 变量

#### 2. 订阅链接无法访问
**可能原因**:
- UUID格式错误
- 域名解析问题
- Workers服务未启动

**解决方法**:
1. 检查UUID格式是否正确
2. 确认域名已正确绑定
3. 查看Workers日志排查错误

#### 3. 连接速度慢
**优化建议**:
1. 启用调试模式查看IP选择情况
2. 手动指定 `PROXYIP` 为最优IP
3. 调整 `ROUTING_STRATEGY` 为 `fastest`

### 调试模式

启用调试模式可以查看详细的运行日志：

1. 设置环境变量 `DEBUG=true`
2. 重新部署Workers
3. 在浏览器开发者工具中查看Console日志

## 📈 性能优化

### 1. IP池优化
- 定期更新IP池（默认1小时）
- 监控IP健康度评分
- 及时清理失效IP

### 2. 连接优化
- 调整最大连接数限制
- 优化连接超时时间
- 启用连接复用

### 3. 缓存优化
- 利用Cloudflare边缘缓存
- 优化静态资源加载
- 减少API调用频率

## 🔒 安全建议

1. **定期更换UUID**: 建议每月更换一次UUID
2. **监控访问日志**: 定期检查异常访问模式
3. **限制访问来源**: 使用 `ALLOWED_COUNTRIES` 限制访问国家
4. **启用伪装**: 确保伪装功能正常工作
5. **更新及时**: 关注项目更新，及时升级

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## ⚠️ 免责声明

本项目仅供学习和研究使用，请遵守当地法律法规。使用本项目所产生的任何后果由使用者自行承担。

---

**NeoEdge Proxy** - 让网络访问更智能、更安全、更稳定！
